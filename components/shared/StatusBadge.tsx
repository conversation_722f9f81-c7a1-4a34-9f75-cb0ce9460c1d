"use client";

import React from "react";

interface StatusBadgeProps {
  status: string;
  variant?: "default" | "group" | "status";
}

export function StatusBadge({ status, variant = "default" }: StatusBadgeProps) {
  const getStatusStyles = () => {
    if (variant === "status") {
      const statusStyles = {
        Active: "bg-green-100 text-green-800",
        Inactive: "bg-red-100 text-red-800",
        active: "bg-green-100 text-green-800",
        inactive: "bg-red-100 text-red-800",
      };
      return statusStyles[status as keyof typeof statusStyles] || "bg-gray-100 text-gray-800";
    }

    if (variant === "group") {
      // Generate a consistent color based on the group name
      const colors = [
        "bg-blue-100 text-blue-800",
        "bg-purple-100 text-purple-800", 
        "bg-red-100 text-red-800",
        "bg-green-100 text-green-800",
        "bg-yellow-100 text-yellow-800",
        "bg-indigo-100 text-indigo-800",
        "bg-pink-100 text-pink-800",
        "bg-gray-100 text-gray-800",
      ];
      
      // Simple hash function to get consistent color for each group
      let hash = 0;
      for (let i = 0; i < status.length; i++) {
        hash = ((hash << 5) - hash + status.charCodeAt(i)) & 0xffffffff;
      }
      return colors[Math.abs(hash) % colors.length];
    }

    // Default variant
    return "bg-gray-100 text-gray-800";
  };

  return (
    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusStyles()}`}>
      {status}
    </span>
  );
}

export default StatusBadge;
