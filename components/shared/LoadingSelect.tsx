"use client";

import React from "react";
import { SelectOption } from "@/types";

interface LoadingSelectProps {
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: SelectOption[];
  loading: boolean;
  error?: string | null;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
}

export function LoadingSelect({
  label,
  value,
  onChange,
  options,
  loading,
  error,
  placeholder,
  required = false,
  disabled = false,
}: LoadingSelectProps) {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(e.target.value);
  };

  if (loading) {
    return (
      <div className="space-y-1">
        <label className="text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <select className="w-full p-2 border rounded-md" disabled>
          <option>Loading {label.toLowerCase()}...</option>
        </select>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-1">
        <label className="text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
        <select className="w-full p-2 border rounded-md border-red-300" disabled>
          <option>Error loading {label.toLowerCase()}</option>
        </select>
        <span className="text-sm text-red-500">{error}</span>
      </div>
    );
  }

  return (
    <div className="space-y-1">
      <label className="text-sm font-medium text-gray-700">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      <select
        className="w-full p-2 border rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
        value={value}
        onChange={handleChange}
        required={required}
        disabled={disabled}
      >
        <option value="">
          {placeholder || `Select ${label.toLowerCase()}`}
        </option>
        {options.map((option) => (
          <option key={option.value} value={option.value}>
            {option.label}
          </option>
        ))}
      </select>
    </div>
  );
}

export default LoadingSelect;
