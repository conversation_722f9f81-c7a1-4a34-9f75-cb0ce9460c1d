"use client";

import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { PasswordStrength } from "@/types";

interface PasswordInputProps {
  value: string;
  onChange: (password: string) => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  showStrength?: boolean;
  strengthConfig?: {
    minLength?: number;
    requireUppercase?: boolean;
    requireNumber?: boolean;
    requireSpecial?: boolean;
  };
}

export function PasswordInput({
  value,
  onChange,
  placeholder = "Password",
  required = false,
  disabled = false,
  showStrength = true,
  strengthConfig = {
    minLength: 8,
    requireUppercase: true,
    requireNumber: true,
    requireSpecial: false,
  },
}: PasswordInputProps) {
  const [showPassword, setShowPassword] = useState(false);

  const calculateStrength = (): PasswordStrength => {
    return {
      length: value.length >= (strengthConfig.minLength || 8),
      uppercase: strengthConfig.requireUppercase ? /[A-Z]/.test(value) : true,
      number: strengthConfig.requireNumber ? /\d/.test(value) : true,
      special: strengthConfig.requireSpecial ? /[!@#$%^&*(),.?":{}|<>]/.test(value) : true,
    };
  };

  const strength = calculateStrength();
  const isValid = Object.values(strength).every(Boolean);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  return (
    <div className="space-y-2">
      <div className="relative">
        <Input
          type={showPassword ? "text" : "password"}
          value={value}
          onChange={handleChange}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-2 top-1/2 -translate-y-1/2 h-8"
          onClick={() => setShowPassword(!showPassword)}
          disabled={disabled}
        >
          {showPassword ? "Hide" : "Show"}
        </Button>
      </div>
      
      {showStrength && value && (
        <div className="space-y-1">
          <div className="flex gap-2 text-sm">
            {strengthConfig.minLength && (
              <span className={strength.length ? "text-green-600" : "text-gray-500"}>
                • {strengthConfig.minLength}+ characters
              </span>
            )}
            {strengthConfig.requireUppercase && (
              <span className={strength.uppercase ? "text-green-600" : "text-gray-500"}>
                • Uppercase
              </span>
            )}
            {strengthConfig.requireNumber && (
              <span className={strength.number ? "text-green-600" : "text-gray-500"}>
                • Number
              </span>
            )}
            {strengthConfig.requireSpecial && (
              <span className={strength.special ? "text-green-600" : "text-gray-500"}>
                • Special character
              </span>
            )}
          </div>
          
          {/* Strength indicator bar */}
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className={`h-2 rounded-full transition-all duration-300 ${
                isValid
                  ? "bg-green-500"
                  : Object.values(strength).filter(Boolean).length > 1
                  ? "bg-yellow-500"
                  : "bg-red-500"
              }`}
              style={{
                width: `${(Object.values(strength).filter(Boolean).length / Object.values(strength).length) * 100}%`,
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}

export default PasswordInput;
