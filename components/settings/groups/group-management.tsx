// components/group-management.tsx
"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Cookies from "js-cookie";
import apiClient from "@/lib/apiClient";
import { DeleteGroupDialog } from "@/app/app/settings/groups/page";

export interface group {
  id: number;
  name: string;
}

// group Table Component
export function GroupTable({
  onDelete,
  groups,
  setGroups,
}: {
  onDelete: (id: number) => void;
  groups: group[];
  setGroups: (groups: group[]) => void;
}) {
  const access_token = Cookies.get("accessToken");

  useEffect(() => {
    const fetchGroups = async () => {
      try {
        const response = await apiClient.get("/group", {
          headers: {
            Authorization: `Bear<PERSON> ${access_token}`,
          },
          cache: "no-store",
        });
        setGroups(response.data.data);
        console.log(response.data.data);
      } catch (error) {
        console.error("Failed to fetch group:", error);
      }
    };

    fetchGroups();
  }, []);

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="rounded-2xl shadow-lg bg-white dark:bg-gray-900 overflow-hidden">
        <Table className="w-full">
          <TableHeader className="bg-gray-100 dark:bg-gray-800">
            <TableRow>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                S.N.
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Group Name
              </TableHead>
              <TableHead className="text-right text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {groups.map((group, index) => (
              <GroupRow
                key={group.id}
                group={group}
                index={index}
                onDelete={onDelete}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// group Row Component
function GroupRow({
  group,
  index,
  onDelete,
}: {
  group: group;
  index: number;
  onDelete: (id: number) => void;
}) {
  const router = useRouter();

  return (
    <TableRow>
      <TableCell>{index + 1}</TableCell>
      <TableCell>{group.name}</TableCell>
      <TableCell className="text-right space-x-2">
        <Button
          variant="outline"
          size="ss"
          onClick={() => router.push(`/app/settings/groups/edit/${group.id}`)}
        >
          Edit
        </Button>
        <DeleteGroupDialog
          name={group.name}
          onDelete={() => onDelete(group.id)}
        />
      </TableCell>
    </TableRow>
  );
}

// // Add group Form Component
export function AddGroupForm({
  onSubmit,
}: {
  onSubmit: (groupname: group) => void;
}) {
  const [newgroup, setNewgroup] = useState({
    groupname: "",
  });
  const isformValid = newgroup.groupname.trim() !== "";

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...newgroup,
          groupname: newgroup.groupname.trim(),
        });
        setNewgroup({
          groupname: "",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Group Name"
        value={newgroup.groupname}
        onChange={(e) =>
          setNewgroup((prev) => ({ ...prev, groupname: e.target.value }))
        }
      />

      <Button type="submit" className="w-full" disabled={!isformValid}>
        Add group
      </Button>
    </form>
  );
}
