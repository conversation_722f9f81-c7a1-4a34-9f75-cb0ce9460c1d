// components/settings/users/user-management.tsx
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DeleteUserDialog } from "@/app/app/settings/users/page";
import StatusBadge from "@/components/shared/StatusBadge";
import { User, UserGroup, StatusGroup } from "@/types";

// Re-export for backward compatibility
export type { User, UserGroup, StatusGroup };

// User Table Component
export function UserTable({
  users,
  onDelete,
}: {
  users: User[];
  onDelete: (id: number) => void;
}) {
  const router = useRouter();
  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="rounded-2xl shadow-lg bg-white dark:bg-gray-900 overflow-hidden">
        <Table className="w-full">
          <TableHeader className="bg-gray-100 dark:bg-gray-800">
            <TableRow>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                S.N.
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Username
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Email
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-3 py-4">
                Status
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Group
              </TableHead>
              <TableHead className="text-right text-gray-700 dark:text-gray-300 font-semibold text-lg px-6 py-4">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users?.map((user, index) => (
              <TableRow key={user.id} id={user.id}>
                <TableCell>{index + 1}</TableCell>
                <TableCell>{user.username}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <StatusBadge
                    status={user.status || "Inactive"}
                    variant="status"
                  />
                </TableCell>
                <TableCell>
                  <StatusBadge status={user.group} variant="group" />
                </TableCell>
                <TableCell className="text-right space-x-2">
                  <Button
                    variant="outline"
                    size="ss"
                    onClick={() =>
                      router.push(`/app/settings/users/edit/${user.id}`)
                    }
                  >
                    Edit
                  </Button>
                  <DeleteUserDialog
                    username={user.username}
                    onDelete={() => onDelete(user.id)}
                  />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// Add User Form Component
export function AddUserForm({ onSubmit }: { onSubmit: (user: User) => void }) {
  const [newUser, setNewUser] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
    group: "user" as UserGroup,
  });

  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    uppercase: false,
    number: false,
  });

  const handlePasswordChange = (password: string) => {
    setPasswordStrength({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
    });
    setNewUser((prev) => ({ ...prev, password }));
  };

  const isValid =
    newUser.username.trim() !== "" &&
    passwordStrength.length &&
    passwordStrength.uppercase &&
    passwordStrength.number &&
    newUser.password === newUser.confirmPassword;

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...newUser,
          username: newUser.username.trim(),
        });
        setNewUser({
          username: "",
          password: "",
          confirmPassword: "",
          group: "user",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Username"
        value={newUser.username}
        onChange={(e) =>
          setNewUser((prev) => ({ ...prev, username: e.target.value }))
        }
      />

      <PasswordInput
        value={newUser.password}
        onChange={handlePasswordChange}
        strength={passwordStrength}
      />

      <Input
        type="password"
        placeholder="Confirm Password"
        value={newUser.confirmPassword}
        onChange={(e) =>
          setNewUser((prev) => ({ ...prev, confirmPassword: e.target.value }))
        }
      />

      <groupSelect
        value={newUser.group}
        onChange={(group) => setNewUser((prev) => ({ ...prev, group }))}
      />

      <Button type="submit" className="w-full" disabled={!isValid}>
        Create User
      </Button>
    </form>
  );
}

// Shared Components are now imported from @/components/shared

// Note: groupSelect function removed as it's not being used
// Group selection is now handled by dynamic components in add-user.tsx and edit-user.tsx

function PasswordInput({
  value,
  onChange,
  strength,
  placeholder = "Password",
}: {
  value: string;
  onChange: (password: string) => void;
  strength: { length: boolean; uppercase: boolean; number: boolean };
  placeholder?: string;
}) {
  return (
    <div className="space-y-2">
      <Input
        type="password"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
      <div className="flex gap-2 text-sm">
        <span className={strength.length ? "text-green-600" : "text-gray-500"}>
          • 8+ characters
        </span>
        <span
          className={strength.uppercase ? "text-green-600" : "text-gray-500"}
        >
          • Uppercase
        </span>
        <span className={strength.number ? "text-green-600" : "text-gray-500"}>
          • Number
        </span>
      </div>
    </div>
  );
}
