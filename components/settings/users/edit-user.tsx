"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { User, UserGroup, Organization } from "./user-management";

export interface EditUserFormProps {
  user: User;
  onSubmit: (user: User) => void;
}

export function EditUserForm({ user, onSubmit }: EditUserFormProps) {
  const [editedUser, setEditedUser] = useState(user);
  const [editedEmail, setEditedEmail] = useState(user.email);
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false); 

  const passwordStrength = {
    length: newPassword.length >= 8,
    uppercase: /[A-Z]/.test(newPassword),
    number: /\d/.test(newPassword),
  };

  const isValid =
    editedUser.username.trim() !== "" &&
    (newPassword === "" ||
      (passwordStrength.length &&
        passwordStrength.uppercase &&
        passwordStrength.number &&
        confirmPassword !== "" &&
        newPassword === confirmPassword));

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({
      ...editedUser,
      password: newPassword || editedUser.password,
      email: editedEmail,
      organization: editedUser.organization,
    });
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Username</label>
        <Input
          value={editedUser.username}
          onChange={(e) =>
            setEditedUser((prev) => ({ ...prev, username: e.target.value }))
          }
          placeholder="Username"
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">
          New Password
        </label>
        <PasswordInput
          value={newPassword}
          onChange={(password) => setNewPassword(password)}
          strength={passwordStrength}
          placeholder="New Password (leave blank to keep current)"
          showPassword={showPassword}
          setShowPassword={setShowPassword}
        />
      </div>

      {newPassword && (
        <div className="space-y-2">
          <label className="text-sm font-medium text-gray-700">
            Confirm New Password
          </label>
          <div className="relative">
            <Input
              type={showConfirmPassword ? "text" : "password"}
              placeholder="Confirm New Password"
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />
            <Button
              type="button"
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 -translate-y-1/2 h-8"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
            >
              {showConfirmPassword ? "Hide" : "Show"}
            </Button>
          </div>
        </div>
      )}
      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Email</label>
        <Input
          value={editedEmail}
          onChange={(e) => setEditedEmail(e.target.value)}
          placeholder="Email Address"
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Organization</label>
        <OrganizationSelect
          value={editedUser.organization}
          onChange={(organization) =>
            setEditedUser((prev) => ({ ...prev, organization }))
          }
        />
      </div>

      <div className="space-y-2">
        <label className="text-sm font-medium text-gray-700">Group</label>
        <GroupSelect
          value={editedUser.group}
          onChange={(group) => setEditedUser((prev) => ({ ...prev, group }))}
        />
      </div>

      <Button type="submit" className="w-full" disabled={!isValid}>
        Save Changes
      </Button>
    </form>
  );
}

function PasswordInput({
  value,
  onChange,
  strength,
  placeholder = "Password",
  showPassword,
  setShowPassword,
}: {
  value: string;
  onChange: (password: string) => void;
  strength: { length: boolean; uppercase: boolean; number: boolean };
  placeholder?: string;
  showPassword?: boolean;
  setShowPassword?: (showPassword: boolean) => void;
}) {
  const [localShowPassword, setLocalShowPassword] = useState(false);
  
  // Use provided state or local state
  const isPasswordVisible = showPassword !== undefined ? showPassword : localShowPassword;
  const togglePasswordVisibility = setShowPassword || setLocalShowPassword;
  
  return (
    <div className="space-y-2">
      <div className="relative">
        <Input
          placeholder={placeholder}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          type={isPasswordVisible ? "text" : "password"}
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-2 top-1/2 -translate-y-1/2 h-8"
          onClick={() => togglePasswordVisibility(!isPasswordVisible)}
        >
          {isPasswordVisible ? "Hide" : "Show"}
        </Button>
      </div>
      {value && (
        <div className="flex gap-2 text-sm">
          <span className={strength.length ? "text-green-600" : "text-gray-500"}>
            • 8+ characters
          </span>
          <span
            className={strength.uppercase ? "text-green-600" : "text-gray-500"}
          >
            • Uppercase
          </span>
          <span className={strength.number ? "text-green-600" : "text-gray-500"}>
            • Number
          </span>
        </div>
      )}
    </div>
  );
}

function OrganizationSelect({
  value,
  onChange,
}: {
  value: string;
  onChange: (organization: string) => void;
}) {
  return (
    <select
      className="w-full p-2 border rounded-md"
      value={value}
      onChange={(e) => onChange(e.target.value)}
    >
      <option value="Workalaya">Workalaya</option>
      <option value="Sky Broadband">Sky Broadband</option>
      <option value="CNC">CNC</option>
    </select>
  );
}

function GroupSelect({
  value,
  onChange,
}: {
  value: UserGroup;
  onChange: (group: UserGroup) => void;
}) {
  return (
    <select
      className="w-full p-2 border rounded-md"
      value={value}
      onChange={(e) => onChange(e.target.value as UserGroup)}
    >
      <option value="user">User</option>
      <option value="admin">Admin</option>
      <option value="superadmin">Super Admin</option>
      <option value="sales">Sales</option>
      <option value="support">Support</option>
    </select>
  );
}
