"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

export type UserGroup = "user" | "admin" | "superadmin" | "sales" | "support";

export interface User {
  id: number;
  username: string;
  email: string;
  group: UserGroup;
  password: string;
  organization: string;
}

export function AddUserForm({ onSubmit }: { onSubmit: (user: User) => void }) {
  const [newUser, setNewUser] = useState({
    username: "",
    password: "",
    confirmPassword: "",
    email: "",
    organization: "",
    group: "user" as UserGroup,
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const [passwordStrength, setPasswordStrength] = useState({
    length: false,
    uppercase: false,
    number: false,
  });

  const handlePasswordChange = (password: string) => {
    setPasswordStrength({
      length: password.length >= 8,
      uppercase: /[A-Z]/.test(password),
      number: /\d/.test(password),
    });
    setNewUser((prev) => ({ ...prev, password }));
  };

  const isValid =
    newUser.username.trim() !== "" &&
    passwordStrength.length &&
    passwordStrength.uppercase &&
    passwordStrength.number &&
    newUser.password === newUser.confirmPassword;

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...newUser,
          username: newUser.username.trim(),
        });
        setNewUser({
          username: "",
          password: "",
          confirmPassword: "",
          group: "user",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Username"
        value={newUser.username}
        onChange={(e) =>
          setNewUser((prev) => ({ ...prev, username: e.target.value }))
        }
      />

      <div className="relative">
        <Input
          type={showPassword ? "text" : "password"}
          placeholder="Password"
          value={newUser.password}
          onChange={(e) => handlePasswordChange(e.target.value)}
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-2 top-1/2 -translate-y-1/2 h-8"
          onClick={() => setShowPassword(!showPassword)}
        >
          {showPassword ? "Hide" : "Show"}
        </Button>
      </div>

      <div className="relative">
        <Input
          type={showConfirmPassword ? "text" : "password"}
          placeholder="Confirm Password"
          value={newUser.confirmPassword}
          onChange={(e) =>
            setNewUser((prev) => ({ ...prev, confirmPassword: e.target.value }))
          }
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-2 top-1/2 -translate-y-1/2 h-8"
          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
        >
          {showConfirmPassword ? "Hide" : "Show"}
        </Button>
      </div>
      <div className="space-y-2">
        <Input
          type="email"
          placeholder="Email Address"
          value={newUser.email}
          pattern="[a-z0-9._%+-]+@[a-z0-9.-]+\.[a-z]{2,}$"
          required
          onChange={(e) =>
            setNewUser((prev) => ({ ...prev, email: e.target.value }))
          }
        />
        {newUser.email && !newUser.email.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/) && (
          <span className="text-sm text-red-500">Please enter a valid email</span>
        )}
      </div>
      <OrganizationSelect
        value={newUser.organization}
        onChange={(organization) =>
          setNewUser((prev) => ({ ...prev, organization }))
        }
      />

      <GroupSelect
        value={newUser.group}
        onChange={(group) => setNewUser((prev) => ({ ...prev, group }))}
      />

      <Button type="submit" className="w-full" disabled={!isValid}>
        Create User
      </Button>
    </form>
  );
}

function PasswordInput({
  value,
  onChange,
  strength,
  placeholder = "Password",
}: {
  value: string;
  onChange: (password: string) => void;
  strength: { length: boolean; uppercase: boolean; number: boolean };
  placeholder?: string;
}) {
  return (
    <div className="space-y-2">
      <Input
        type="password"
        placeholder={placeholder}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
      <div className="flex gap-2 text-sm">
        <span className={strength.length ? "text-green-600" : "text-gray-500"}>
          • 8+ characters
        </span>
        <span
          className={strength.uppercase ? "text-green-600" : "text-gray-500"}
        >
          • Uppercase
        </span>
        <span className={strength.number ? "text-green-600" : "text-gray-500"}>
          • Number
        </span>
      </div>
    </div>
  );
}

function GroupSelect({
  value,
  onChange,
}: {
  value: UserGroup;
  onChange: (group: UserGroup) => void;
}) {
  return (
    <select
      className="w-full p-2 border rounded-md"
      value={value}
      onChange={(e) => onChange(e.target.value as UserGroup)}
    >
      <option value="user">User</option>
      <option value="admin">Admin</option>
      <option value="superadmin">Super Admin</option>
      <option value="sales">Sales</option>
      <option value="support">Support</option>
    </select>
  );
}

function OrganizationSelect({
  value,
  onChange,
}: {
  value: string;
  onChange: (organization: string) => void;
}) {
  return (
    <select
      className="w-full p-2 border rounded-md"
      value={value}
      onChange={(e) => onChange(e.target.value)}
    >
      <option value="Workalaya">Workalaya</option>
      <option value="Sky Broadband">Sky Broadband</option>
      <option value="CNC">CNC</option>
    </select>
  );
}
