"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useGroups } from "@/hooks/useGroups";
import { useOrganizations } from "@/hooks/useOrganizations";
import { useForm } from "@/hooks/useForm";
import {
  validationSchemas,
  validatePasswordConfirmation,
} from "@/utils/validation";
import Form<PERSON>ield from "@/components/shared/FormField";
import LoadingSelect from "@/components/shared/LoadingSelect";
import PasswordInput from "@/components/shared/PasswordInput";
import { User, CreateUserData, SelectOption } from "@/types";

// Re-export types for backward compatibility
export type UserGroup = string;
export interface Group {
  id: number;
  name: string;
}

export function AddUserForm({ onSubmit }: { onSubmit: (user: User) => void }) {
  // Fetch groups and organizations
  const {
    data: groups,
    loading: loadingGroups,
    error: groupsError,
  } = useGroups();
  const {
    data: organizations,
    loading: loadingOrganizations,
    error: organizationsError,
  } = useOrganizations();

  // Form state and validation
  const form = useForm<CreateUserData & { confirmPassword: string }>({
    initialValues: {
      username: "",
      email: "",
      password: "",
      confirmPassword: "",
      group: "",
      organization: "",
    },
    validationSchema: {
      ...validationSchemas.user,
      confirmPassword: {
        required: true,
        custom: (value: string) =>
          validatePasswordConfirmation(form.values.password, value),
      },
    },
    onSubmit: async (data) => {
      const { confirmPassword, ...userData } = data;
      onSubmit({
        id: Date.now(),
        ...userData,
      } as User);
      form.reset();
    },
  });

  // Convert groups and organizations to select options
  const groupOptions: SelectOption[] =
    groups?.map((group) => ({
      value: group.name,
      label: group.name,
    })) || [];

  const organizationOptions: SelectOption[] =
    organizations?.map((org) => ({
      value: org.name,
      label: org.name,
    })) || [];

  return (
    <form onSubmit={form.handleSubmit} className="space-y-4">
      <FormField
        label="Username"
        name="username"
        type="text"
        placeholder="Enter username"
        required
        value={form.values.username}
        onChange={form.handleChange("username")}
        error={form.errors.username}
      />

      <FormField
        label="Email"
        name="email"
        type="email"
        placeholder="Enter email address"
        required
        value={form.values.email}
        onChange={form.handleChange("email")}
        error={form.errors.email}
      />

      <div className="space-y-1">
        <label className="text-sm font-medium text-gray-700">
          Password <span className="text-red-500">*</span>
        </label>
        <PasswordInput
          value={form.values.password}
          onChange={form.handleChange("password")}
          placeholder="Enter password"
          showStrength
        />
        {form.errors.password && (
          <span className="text-sm text-red-500">{form.errors.password}</span>
        )}
      </div>

      <FormField
        label="Confirm Password"
        name="confirmPassword"
        type="password"
        placeholder="Confirm password"
        required
        value={form.values.confirmPassword}
        onChange={form.handleChange("confirmPassword")}
        error={form.errors.confirmPassword}
      />

      <LoadingSelect
        label="Organization"
        value={form.values.organization}
        onChange={form.handleChange("organization")}
        options={organizationOptions}
        loading={loadingOrganizations}
        error={organizationsError}
        required
      />

      <LoadingSelect
        label="Group"
        value={form.values.group}
        onChange={form.handleChange("group")}
        options={groupOptions}
        loading={loadingGroups}
        error={groupsError}
        required
      />

      <Button
        type="submit"
        className="w-full"
        disabled={form.isSubmitting || !form.isValid}
      >
        {form.isSubmitting ? "Creating..." : "Create User"}
      </Button>
    </form>
  );
}

// All form components are now handled by shared components
