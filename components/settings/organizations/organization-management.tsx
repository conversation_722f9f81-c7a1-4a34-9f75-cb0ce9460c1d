// components/organization-management.tsx
"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Cookies from "js-cookie";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import apiClient from "@/lib/apiClient";

export interface organization {
  id: number;
  name: string;
  parent_id: string;
  type: string;
}

// organization Table Component
export function OrganizationTable({ onDelete, organizations, setOrganizations }: { onDelete: (id: number) => void, organizations: organization[], setOrganizations: (organizations: organization[]) => void }) {

  // const [organizations, setOrganizations] = useState<organization[]>([]);
  const access_token = Cookies.get("accessToken");

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        const response = await apiClient.get("/organization", {
          headers: {
            Authorization: `Bearer ${access_token}`,
          },
          cache: "no-store",
        });
        setOrganizations(response.data.data);
        console.log(response.data.data);
      } catch (error) {
        console.error("Failed to fetch group:", error);
      }
    };

    fetchOrganizations();
  }, []);

  // useEffect(() => {
  //   const fetchOrganizations = async () => {
  //     try {
  //       const response = await fetch("http://192.168.17.149:9000/api/v1/organization", {
  //         method: "GET",

  //         headers: {
  //           authorization: `Bearer ${access_token}`,
  //         },
  //       });
  //       const data = await response.json();
  //       setOrganizations(data.data);
  //       console.log(data.data);
  //     } catch (error) {
  //       console.error("Failed to fetch organizations:", error);
  //     }
  //   };
  //   fetchOrganizations();
  // }, []);

  return (
    <div className="w-full max-w-4xl mx-auto p-4">
      <div className="rounded-2xl shadow-lg bg-white dark:bg-gray-900 overflow-hidden">
        <Table className="w-full">
          <TableHeader className="bg-gray-100 dark:bg-gray-800">
            <TableRow>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-3 py-4">
                S.N.
              </TableHead>
              <TableHead className="text-left text-gray-700 dark:text-gray-300 font-semibold text-lg px-3 py-4">
                Organization Name
              </TableHead>
              <TableHead className="text-right text-gray-700 dark:text-gray-300 font-semibold text-lg px-10 py-4">
                Actions
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {organizations.map((organization, index) => (
              <OrganizationRow
                key={organization.id}
                index={index}
                organization={organization}
                onDelete={onDelete}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

// organization Row Component
function OrganizationRow({
  organization,
  index,
  onDelete,
}: {
  organization: organization;
  index: number;
  onDelete: (id: number) => void;
}) {
  const router = useRouter();

  return (
    <TableRow>
      <TableCell>{index +1}</TableCell>
      <TableCell>{organization.name}</TableCell>
      <TableCell className="text-right space-x-2">
        <Button
          variant="outline"
          size="ss"
          onClick={() =>
            router.push(`/app/settings/organizations/edit/${organization.id}`)
          }
        >
          Edit
        </Button>
        <Button
          variant="destructive"
          size="ss"
          onClick={() => onDelete(organization.id)}
        >
          Delete
        </Button>
      </TableCell>
    </TableRow>
  );
}

// // Add organization Form Component
export function AddOrganizationForm({
  onSubmit,
}: {
  onSubmit: (organization: organization) => void;
}) {
  const [neworganization, setNeworganization] = useState({
    name: "",
  });

  return (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit({
          id: Date.now(),
          ...neworganization,
          name: neworganization.name.trim(),
        });
        setNeworganization({
          name: "",
        });
      }}
      className="space-y-4"
    >
      <Input
        placeholder="Organization name"
        value={neworganization.name}
        onChange={(e) =>
          setNeworganization((prev) => ({
            ...prev,
            name: e.target.value,
          }))
        }
      />

      <Button type="submit" className="w-full">
        Add organization
      </Button>
    </form>
  );
}
