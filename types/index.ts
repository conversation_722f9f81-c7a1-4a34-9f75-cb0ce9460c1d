// Shared types and interfaces for the application

export interface BaseEntity {
  id: number;
}

// User related types
export interface User extends BaseEntity {
  username: string;
  email: string;
  group: string;
  password: string;
  organization: string;
  status?: "Active" | "Inactive";
}

export type UserGroup = string;
export type StatusGroup = "Active" | "Inactive";

// Group related types
export interface Group extends BaseEntity {
  name: string;
}

// Organization related types
export interface Organization extends BaseEntity {
  name: string;
  parent_id: string;
}

// Form data types
export interface CreateUserData {
  username: string;
  email: string;
  password: string;
  group: string;
  organization: string;
}

export interface UpdateUserData {
  id: number;
  username: string;
  email: string;
  group: string;
  organization: string;
  password?: string;
}

export interface CreateGroupData {
  groupname: string;
}

export interface UpdateGroupData {
  id: number;
  groupname: string;
}

export interface CreateOrganizationData {
  organizationname: string;
  parent_id?: string;
}

export interface UpdateOrganizationData {
  id: number;
  organizationname: string;
  parent_id?: string;
}

// API response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success?: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
}

// Form validation types
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationErrors {
  [key: string]: string;
}

// Component prop types
export interface SelectOption {
  value: string;
  label: string;
}

export interface TableColumn<T> {
  key: keyof T;
  label: string;
  render?: (value: any, item: T) => React.ReactNode;
  sortable?: boolean;
}

export interface FormFieldProps {
  label: string;
  name: string;
  type?: "text" | "email" | "password" | "select" | "textarea";
  placeholder?: string;
  required?: boolean;
  options?: SelectOption[];
  value: any;
  onChange: (value: any) => void;
  error?: string;
  disabled?: boolean;
}

// Password strength types
export interface PasswordStrength {
  length: boolean;
  uppercase: boolean;
  number: boolean;
  special?: boolean;
}

// Common component props
export interface LoadingProps {
  loading: boolean;
  children: React.ReactNode;
}

export interface ErrorProps {
  error: string | null;
  onRetry?: () => void;
}

export interface ConfirmDialogProps {
  open: boolean;
  title: string;
  message: string;
  confirmText?: string;
  cancelText?: string;
  onConfirm: () => void;
  onCancel: () => void;
  destructive?: boolean;
}

// Search and filter types
export interface SearchFilters {
  query: string;
  status?: string;
  group?: string;
  organization?: string;
}

export interface SortConfig {
  key: string;
  direction: "asc" | "desc";
}

// Navigation types
export interface BreadcrumbItem {
  label: string;
  href?: string;
}

// Theme and styling types
export interface ThemeColors {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
}

export interface BadgeVariant {
  color: string;
  backgroundColor: string;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// Event handler types
export type ChangeHandler<T = string> = (value: T) => void;
export type SubmitHandler<T> = (data: T) => void | Promise<void>;
export type ClickHandler = () => void;
export type AsyncClickHandler = () => Promise<void>;
