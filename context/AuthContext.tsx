"use client";

import type { ReactNode } from "react";
import React, { createContext, useContext, useState, useEffect, useRef } from "react";
import apiClient, { setRefreshTokenInstance } from "@/lib/apiClient";
import { useRefreshToken } from "@/hooks/useRefreshToken";

interface AuthContextType {
  isLoggedIn: boolean;
  isAuthReady: boolean;
  login: (username: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
  user?: {
    group?: string;
    actions?: string[];
  };
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Token validation utilities
const TokenUtils = {
  decodeToken: (token: string) => {
    try {
      return JSON.parse(atob(token.split(".")[1]));
    } catch (error) {
      return null;
    }
  },

  isTokenValid: (token: string) => {
    const payload = TokenUtils.decodeToken(token);
    if (!payload) return false;

    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();
    // Consider token valid if it has at least 5 minutes remaining
    return expirationTime - currentTime > 5 * 60 * 1000;
  }
};

export function AuthProvider({ children }: { children: ReactNode }) {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [isAuthReady, setIsAuthReady] = useState(false);
  const [user, setUser] = useState<AuthContextType['user']>();
  const tokenManager = useRefreshToken();
  const isCheckingAuth = useRef(false);
  const initialAuthCheckDone = useRef(false);

  // Register the refresh token function with the API client
  useEffect(() => {
    setRefreshTokenInstance(async () => {
      try {
        const success = await tokenManager.refreshToken();
        return { success };
      } catch (error: any) {
        console.error("Token refresh failed:", error);
        // If we get a 403, the refresh token is invalid
        if (error.response?.status === 403) {
          tokenManager.clearTokens();
        }
        return { success: false, error };
      }
    });
  }, [tokenManager]);

  // Check auth status on mount
  useEffect(() => {
    const checkAuth = async () => {
      // Prevent multiple simultaneous auth checks
      if (isCheckingAuth.current) {
        console.log("Auth check already in progress, skipping...");
        return;
      }

      // Skip if we've already done the initial check
      if (initialAuthCheckDone.current) {
        console.log("Initial auth check already done, skipping...");
        return;
      }

      try {
        isCheckingAuth.current = true;
        console.log("Starting auth check...");
        setIsAuthReady(false);
        const { accessToken, refreshToken } = tokenManager.getTokens();
        
        console.log("Auth check tokens:", {
          hasAccessToken: !!accessToken,
          hasRefreshToken: !!refreshToken,
          accessTokenValid: accessToken ? TokenUtils.isTokenValid(accessToken) : false,
          accessTokenExpiry: accessToken ? new Date(TokenUtils.decodeToken(accessToken)?.exp * 1000) : null,
          refreshTokenExpiry: refreshToken ? new Date(TokenUtils.decodeToken(refreshToken)?.exp * 1000) : null
        });

        // First check if current access token is valid
        if (accessToken && TokenUtils.isTokenValid(accessToken)) {
          console.log("Using existing valid access token");
          setIsLoggedIn(true);
          initialAuthCheckDone.current = true;

          // Try to refresh in the background if we have a refresh token
          if (refreshToken) {
            console.log("Attempting background token refresh...");
            try {
              const success = await tokenManager.refreshToken();
              console.log("Background token refresh result:", success);
            } catch (error: any) {
              console.error("Background token refresh error:", error);
              // Just log the error, don't affect login state
            }
          }
          return;
        }

        // If access token is invalid, try to refresh
        if (refreshToken) {
          console.log("Access token invalid, attempting refresh...");
          try {
            const success = await tokenManager.refreshToken();
            console.log("Token refresh result:", success);
            if (success) {
              setIsLoggedIn(true);
              initialAuthCheckDone.current = true;
              return;
            }
          } catch (error: any) {
            console.error("Token refresh error:", error);
            // If we get a 403, the refresh token is invalid
            if (error.response?.status === 403) {
              console.log("Refresh token invalid, logging out");
              tokenManager.clearTokens();
              setIsLoggedIn(false);
              return;
            }
          }
        }

        // If we get here, we have no valid tokens
        console.log("No valid tokens, logging out");
        tokenManager.clearTokens();
        setIsLoggedIn(false);
      } catch (error: any) {
        console.error("Auth check error:", error);
        // Only clear tokens if we have no valid access token
        const { accessToken } = tokenManager.getTokens();
        if (!accessToken || !TokenUtils.isTokenValid(accessToken)) {
          console.log("No valid access token, logging out");
          tokenManager.clearTokens();
          setIsLoggedIn(false);
        } else {
          console.log("Keeping valid access token despite error");
          setIsLoggedIn(true);
        }
      } finally {
        setIsAuthReady(true);
        isCheckingAuth.current = false;
        initialAuthCheckDone.current = true;
      }
    };

    checkAuth();
  }, []); 

  const login = async (username: string, password: string) => {
    try {
      const response = await apiClient.post("/login", { username, password });
      
      if (!response.data.accessToken || !response.data.refreshToken) {
        throw new Error("Invalid response format from server");
      }

      tokenManager.setTokens(response.data);
      
      if (response.data.user) {
        setUser(response.data.user);
      }

      setIsLoggedIn(true);
      initialAuthCheckDone.current = false; 
    } catch (error) {
      tokenManager.clearTokens();
      setIsLoggedIn(false);
      throw error;
    } finally {
      setIsAuthReady(true);
    }
  };

  const logout = async () => {
    try {
      await apiClient.post("/logout");
    } catch (error) {
      console.error("Logout error:", error);
    } finally {
      tokenManager.clearTokens();
      setUser(undefined);
      setIsLoggedIn(false);
      initialAuthCheckDone.current = false; 
    }
  };

  return (
    <AuthContext.Provider value={{ isLoggedIn, isAuthReady, login, logout, user }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
