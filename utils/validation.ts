import { ValidationRule, ValidationSchema, ValidationErrors } from "@/types";

/**
 * Validates a single field value against a validation rule
 */
export function validateField(value: any, rule: ValidationRule): string | null {
  // Required validation
  if (rule.required && (!value || (typeof value === "string" && value.trim() === ""))) {
    return "This field is required";
  }

  // Skip other validations if field is empty and not required
  if (!value || (typeof value === "string" && value.trim() === "")) {
    return null;
  }

  // String-specific validations
  if (typeof value === "string") {
    // Minimum length validation
    if (rule.minLength && value.length < rule.minLength) {
      return `Must be at least ${rule.minLength} characters long`;
    }

    // Maximum length validation
    if (rule.maxLength && value.length > rule.maxLength) {
      return `Must be no more than ${rule.maxLength} characters long`;
    }

    // Pattern validation
    if (rule.pattern && !rule.pattern.test(value)) {
      return "Invalid format";
    }
  }

  // Custom validation
  if (rule.custom) {
    return rule.custom(value);
  }

  return null;
}

/**
 * Validates an entire form object against a validation schema
 */
export function validateForm<T extends Record<string, any>>(
  data: T,
  schema: ValidationSchema
): ValidationErrors {
  const errors: ValidationErrors = {};

  for (const [field, rule] of Object.entries(schema)) {
    const error = validateField(data[field], rule);
    if (error) {
      errors[field] = error;
    }
  }

  return errors;
}

/**
 * Checks if a form has any validation errors
 */
export function hasValidationErrors(errors: ValidationErrors): boolean {
  return Object.keys(errors).length > 0;
}

/**
 * Common validation rules
 */
export const validationRules = {
  required: { required: true },
  
  email: {
    required: true,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    custom: (value: string) => {
      if (!value.match(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)) {
        return "Please enter a valid email address";
      }
      return null;
    },
  },

  password: {
    required: true,
    minLength: 8,
    custom: (value: string) => {
      if (value.length < 8) {
        return "Password must be at least 8 characters long";
      }
      if (!/[A-Z]/.test(value)) {
        return "Password must contain at least one uppercase letter";
      }
      if (!/\d/.test(value)) {
        return "Password must contain at least one number";
      }
      return null;
    },
  },

  username: {
    required: true,
    minLength: 3,
    maxLength: 50,
    pattern: /^[a-zA-Z0-9_-]+$/,
    custom: (value: string) => {
      if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
        return "Username can only contain letters, numbers, hyphens, and underscores";
      }
      return null;
    },
  },

  name: {
    required: true,
    minLength: 2,
    maxLength: 100,
    custom: (value: string) => {
      if (value.trim().length < 2) {
        return "Name must be at least 2 characters long";
      }
      return null;
    },
  },

  optional: {},
};

/**
 * Validation schemas for different forms
 */
export const validationSchemas = {
  user: {
    username: validationRules.username,
    email: validationRules.email,
    password: validationRules.password,
    group: validationRules.required,
    organization: validationRules.required,
  },

  editUser: {
    username: validationRules.username,
    email: validationRules.email,
    group: validationRules.required,
    organization: validationRules.required,
    // Password is optional for edit
    password: {
      minLength: 8,
      custom: (value: string) => {
        if (value && value.length > 0) {
          if (value.length < 8) {
            return "Password must be at least 8 characters long";
          }
          if (!/[A-Z]/.test(value)) {
            return "Password must contain at least one uppercase letter";
          }
          if (!/\d/.test(value)) {
            return "Password must contain at least one number";
          }
        }
        return null;
      },
    },
  },

  group: {
    groupname: validationRules.name,
  },

  organization: {
    organizationname: validationRules.name,
    parent_id: validationRules.optional,
  },
};

/**
 * Password confirmation validation
 */
export function validatePasswordConfirmation(
  password: string,
  confirmPassword: string
): string | null {
  if (password !== confirmPassword) {
    return "Passwords do not match";
  }
  return null;
}

/**
 * Custom validation for specific use cases
 */
export const customValidations = {
  uniqueUsername: (value: string, existingUsernames: string[]) => {
    if (existingUsernames.includes(value.toLowerCase())) {
      return "Username already exists";
    }
    return null;
  },

  uniqueEmail: (value: string, existingEmails: string[]) => {
    if (existingEmails.includes(value.toLowerCase())) {
      return "Email already exists";
    }
    return null;
  },

  strongPassword: (value: string) => {
    const checks = {
      length: value.length >= 8,
      uppercase: /[A-Z]/.test(value),
      lowercase: /[a-z]/.test(value),
      number: /\d/.test(value),
      special: /[!@#$%^&*(),.?":{}|<>]/.test(value),
    };

    const failedChecks = Object.entries(checks)
      .filter(([_, passed]) => !passed)
      .map(([check]) => check);

    if (failedChecks.length > 0) {
      return `Password must include: ${failedChecks.join(", ")}`;
    }

    return null;
  },
};
