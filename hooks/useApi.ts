"use client";

import { useState, useEffect, useCallback } from "react";
import apiClient from "@/lib/apiClient";
import Cookies from "js-cookie";

export interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

export interface ApiOptions {
  immediate?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: string) => void;
}

export interface UseApiReturn<T> extends ApiState<T> {
  execute: () => Promise<void>;
  reset: () => void;
}

/**
 * Generic hook for API operations
 */
export function useApi<T = any>(
  endpoint: string,
  options: ApiOptions = {}
): UseApiReturn<T> {
  const { immediate = true, onSuccess, onError } = options;
  
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
  });

  const execute = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const accessToken = Cookies.get("accessToken");
      const response = await apiClient.get(endpoint, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
        cache: "no-store",
      });
      
      const data = response.data.data || response.data;
      setState({ data, loading: false, error: null });
      
      if (onSuccess) {
        onSuccess(data);
      }
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || "An error occurred";
      setState({ data: null, loading: false, error: errorMessage });
      
      if (onError) {
        onError(errorMessage);
      }
      
      console.error(`Failed to fetch ${endpoint}:`, error);
    }
  }, [endpoint, onSuccess, onError]);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
  }, []);

  useEffect(() => {
    if (immediate) {
      execute();
    }
  }, [execute, immediate]);

  return {
    ...state,
    execute,
    reset,
  };
}

/**
 * Hook for API mutations (POST, PUT, DELETE)
 */
export function useApiMutation<TData = any, TVariables = any>(
  endpoint: string,
  method: "POST" | "PUT" | "DELETE" = "POST",
  options: ApiOptions = {}
) {
  const { onSuccess, onError } = options;
  
  const [state, setState] = useState<ApiState<TData>>({
    data: null,
    loading: false,
    error: null,
  });

  const mutate = useCallback(async (variables?: TVariables) => {
    setState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      const accessToken = Cookies.get("accessToken");
      const config = {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      };

      let response;
      switch (method) {
        case "POST":
          response = await apiClient.post(endpoint, variables, config);
          break;
        case "PUT":
          response = await apiClient.put(endpoint, variables, config);
          break;
        case "DELETE":
          response = await apiClient.delete(endpoint, config);
          break;
        default:
          throw new Error(`Unsupported method: ${method}`);
      }
      
      const data = response.data.data || response.data;
      setState({ data, loading: false, error: null });
      
      if (onSuccess) {
        onSuccess(data);
      }
      
      return data;
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || error.message || "An error occurred";
      setState({ data: null, loading: false, error: errorMessage });
      
      if (onError) {
        onError(errorMessage);
      }
      
      console.error(`Failed to ${method} ${endpoint}:`, error);
      throw error;
    }
  }, [endpoint, method, onSuccess, onError]);

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null });
  }, []);

  return {
    ...state,
    mutate,
    reset,
  };
}
