"use client";

import { useA<PERSON>, useApiMutation } from "./useApi";

export interface User {
  id: number;
  username: string;
  email: string;
  group: string;
  password: string;
  organization: string;
  status?: string;
}

export interface CreateUserData {
  username: string;
  email: string;
  password: string;
  group: string;
  organization: string;
}

export interface UpdateUserData {
  id: number;
  username: string;
  email: string;
  group: string;
  organization: string;
  password?: string;
}

/**
 * Hook for fetching users
 */
export function useUsers() {
  return useApi<User[]>("/users");
}

/**
 * Hook for fetching a single user
 */
export function useUser(userId: number) {
  return useApi<User>(`/users/${userId}`);
}

/**
 * Hook for creating a new user
 */
export function useCreateUser(options?: { onSuccess?: (data: any) => void; onError?: (error: string) => void }) {
  return useApiMutation<User, CreateUserData>("/users", "POST", {
    immediate: false,
    ...options,
  });
}

/**
 * Hook for updating a user
 */
export function useUpdateUser(userId: number, options?: { onSuccess?: (data: any) => void; onError?: (error: string) => void }) {
  return useApiMutation<User, UpdateUserData>(`/users/${userId}`, "PUT", {
    immediate: false,
    ...options,
  });
}

/**
 * Hook for deleting a user
 */
export function useDeleteUser(options?: { onSuccess?: (data: any) => void; onError?: (error: string) => void }) {
  return useApiMutation<void, { id: number }>("/users", "DELETE", {
    immediate: false,
    ...options,
  });
}

/**
 * Combined hook for all user operations
 */
export function useUserOperations() {
  const usersQuery = useUsers();
  
  const createUser = useCreateUser({
    onSuccess: () => {
      usersQuery.execute(); // Refetch users after creation
    },
  });
  
  const updateUser = (userId: number) => useUpdateUser(userId, {
    onSuccess: () => {
      usersQuery.execute(); // Refetch users after update
    },
  });
  
  const deleteUser = useDeleteUser({
    onSuccess: () => {
      usersQuery.execute(); // Refetch users after deletion
    },
  });

  return {
    users: usersQuery,
    createUser,
    updateUser,
    deleteUser,
    refetch: usersQuery.execute,
  };
}
