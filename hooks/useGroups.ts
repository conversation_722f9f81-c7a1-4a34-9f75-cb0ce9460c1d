"use client";

import { useApi, useApiMutation } from "./useApi";

export interface Group {
  id: number;
  name: string;
}

export interface CreateGroupData {
  groupname: string;
}

export interface UpdateGroupData {
  id: number;
  groupname: string;
}

/**
 * Hook for fetching groups
 */
export function useGroups() {
  return useApi<Group[]>("/group");
}

/**
 * Hook for creating a new group
 */
export function useCreateGroup(options?: { onSuccess?: (data: any) => void; onError?: (error: string) => void }) {
  return useApiMutation<Group, CreateGroupData>("/group", "POST", {
    immediate: false,
    ...options,
  });
}

/**
 * Hook for updating a group
 */
export function useUpdateGroup(groupId: number, options?: { onSuccess?: (data: any) => void; onError?: (error: string) => void }) {
  return useApiMutation<Group, UpdateGroupData>(`/group/${groupId}`, "PUT", {
    immediate: false,
    ...options,
  });
}

/**
 * Hook for deleting a group
 */
export function useDeleteGroup(options?: { onSuccess?: (data: any) => void; onError?: (error: string) => void }) {
  return useApiMutation<void, { id: number }>("/group", "DELETE", {
    immediate: false,
    ...options,
  });
}

/**
 * Combined hook for all group operations
 */
export function useGroupOperations() {
  const groupsQuery = useGroups();
  
  const createGroup = useCreateGroup({
    onSuccess: () => {
      groupsQuery.execute(); // Refetch groups after creation
    },
  });
  
  const updateGroup = (groupId: number) => useUpdateGroup(groupId, {
    onSuccess: () => {
      groupsQuery.execute(); // Refetch groups after update
    },
  });
  
  const deleteGroup = useDeleteGroup({
    onSuccess: () => {
      groupsQuery.execute(); // Refetch groups after deletion
    },
  });

  return {
    groups: groupsQuery,
    createGroup,
    updateGroup,
    deleteGroup,
    refetch: groupsQuery.execute,
  };
}
