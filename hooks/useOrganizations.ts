"use client";

import { useApi, useApiMutation } from "./useApi";

export interface Organization {
  id: number;
  name: string;
  parent_id: string;
}

export interface CreateOrganizationData {
  organizationname: string;
  parent_id?: string;
}

export interface UpdateOrganizationData {
  id: number;
  organizationname: string;
  parent_id?: string;
}

/**
 * Hook for fetching organizations
 */
export function useOrganizations() {
  return useApi<Organization[]>("/organization");
}

/**
 * Hook for creating a new organization
 */
export function useCreateOrganization(options?: { onSuccess?: (data: any) => void; onError?: (error: string) => void }) {
  return useApiMutation<Organization, CreateOrganizationData>("/organization", "POST", {
    immediate: false,
    ...options,
  });
}

/**
 * Hook for updating an organization
 */
export function useUpdateOrganization(organizationId: number, options?: { onSuccess?: (data: any) => void; onError?: (error: string) => void }) {
  return useApiMutation<Organization, UpdateOrganizationData>(`/organization/${organizationId}`, "PUT", {
    immediate: false,
    ...options,
  });
}

/**
 * Hook for deleting an organization
 */
export function useDeleteOrganization(options?: { onSuccess?: (data: any) => void; onError?: (error: string) => void }) {
  return useApiMutation<void, { id: number }>("/organization", "DELETE", {
    immediate: false,
    ...options,
  });
}

/**
 * Combined hook for all organization operations
 */
export function useOrganizationOperations() {
  const organizationsQuery = useOrganizations();
  
  const createOrganization = useCreateOrganization({
    onSuccess: () => {
      organizationsQuery.execute(); // Refetch organizations after creation
    },
  });
  
  const updateOrganization = (organizationId: number) => useUpdateOrganization(organizationId, {
    onSuccess: () => {
      organizationsQuery.execute(); // Refetch organizations after update
    },
  });
  
  const deleteOrganization = useDeleteOrganization({
    onSuccess: () => {
      organizationsQuery.execute(); // Refetch organizations after deletion
    },
  });

  return {
    organizations: organizationsQuery,
    createOrganization,
    updateOrganization,
    deleteOrganization,
    refetch: organizationsQuery.execute,
  };
}
