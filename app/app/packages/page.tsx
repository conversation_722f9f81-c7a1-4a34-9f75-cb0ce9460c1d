"use client";

import React from "react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import Cookies from "js-cookie";  
import apiClient from "@/lib/apiClient";

// Sample data - replace with API calls later
// const packages = [
//   {
//     id: 1,
//     type: "package",
//     packagename: "100/100 Mbps",
//     up_speed: "100Mbps",
//     down_speed: "100Mbps",
//     description: "100Mbps package with 50Gbps FUP on a weekly basis",
//     fupdetails: "50Gbps",
//     price: "NPR 700",
//   },
//   {
//     id: 2,
//     type: "package",
//     packagename: "200/200 Mbps",
//     up_speed: "200Mbps",
//     down_speed: "200Mbps",
//     description: "200Mbps package with 100Gbps FUP on a weekly basis",
//     fupdetails: "100Gbps",
//     price: "NPR 1000",
//   },
//   {
//     id: 3,
//     type: "package",
//     packagename: "300/300 Mbps",
//     up_speed: "300Mbps",
//     down_speed: "300Mbps",
//     description: "300Mbps package with 50Gbps FUP on a daily basis",
//     fupdetails: "50Gbps",
//     price: "NPR 1200",
//   },
// ];

interface packages {
  id: number,
  type: string,
  package_name: string,
  upload: string,
  download: string,
  price: string,
}

const durations = [
  {
    id: 1,
    type: "duration",
    duration: "1 month",
    description: "Monthly subscription",
  },
  {
    id: 2,
    type: "duration",
    duration: "3 months",
    description: "Quarterly subscription",
  },
  {
    id: 3,
    type: "duration",
    duration: "6 months",
    description: "Half-yearly subscription",
  },
  {
    id: 4,
    type: "duration",
    duration: "12 months",
    description: "Annual subscription",
  },
];

export default function PackagesPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState<"packages" | "durations">(
    "packages"
  );
  const [search, setSearch] = useState("");

  // State management for packages and durations
  const [packagesList, setPackagesList] = useState<packages[]>([]);
  const [durationsList, setDurationsList] = useState(durations);

  const access_token = Cookies.get("accessToken");

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        const response = await apiClient.get(
          "/package",
          {
            headers: {
              Authorization: `Bearer ${access_token}`,
            },
            cache : "no-store",
          }
        );
        setPackagesList(response.data.data);
        console.log(response.data.data);
      } catch (error) {
        console.error("Failed to fetch packages:", error);
      }
    };

    fetchPackages();
  }, []);

  // useEffect(() => {
  //   const fetchPackages = async () => {
  //     try {
  //       const response = await fetch("http://192.168.17.149:9000/api/v1/package", {
  //         method: "GET",

  //         headers: {
  //           authorization: `Bearer ${access_token}`,
  //         },
  //         cache: "no-store",
  //       });
  //       const data = await response.json();
  //       // if (!response.ok) {
  //       //   throw new Error(`HTTP error! Status: ${response.status}`);
  //       // }
  //       setPackagesList(data.data);
  //       console.log(data.data);
  //     } catch (error) {
  //       console.error("Failed to fetch packages:", error);
  //     }
  //     };
  //   fetchPackages();
  // }, []);

  // Filter data based on search
  const filteredPackages = packagesList?.filter(
    (pkg) =>
      pkg?.package_name.toLowerCase().includes(search.toLowerCase()) ||
      pkg?.price.toLowerCase().includes(search.toLowerCase()) ||
      pkg?.upload.toLowerCase().includes(search.toLowerCase()) || 
      pkg?.download.toLowerCase().includes(search.toLowerCase())
  );

  const filteredDurations = durationsList.filter(
    (duration) =>
      duration.duration.toLowerCase().includes(search.toLowerCase()) ||
      duration.description.toLowerCase().includes(search.toLowerCase())
  );

  // Delete functions
  const handleDeletePackage = (id: number) => {
    if (window.confirm("Are you sure you want to delete this package?")) {
      setPackagesList(packagesList?.filter((pkg) => pkg.id !== id));
    }
  };

  const handleDeleteDuration = (id: number) => {
    if (window.confirm("Are you sure you want to delete this duration?")) {
      setDurationsList(durationsList.filter((duration) => duration.id !== id));
    }
  };

  // Edit functions
  const handleEditPackage = (id: number) => {
    router.push(`/app/packages/edit/${id}`);
  };

  const handleEditDuration = (id: number) => {
  router.push(`/app/packages/edit-duration/${id}`);
  
  
  };

  return (
    <div className="p-6 space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">12,000</div>
            <div className="text-sm text-gray-500">Total Clients</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">8,000 / 12,000</div>
            <div className="text-sm text-gray-500">100/100Mbps</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">3,000 / 12,000</div>
            <div className="text-sm text-gray-500">200/200Mbps</div>
          </div>
        </div>
        <div className="bg-white p-4 rounded shadow flex items-center gap-4">
          <div>
            <div className="text-xl font-bold">1,000 / 12,000</div>
            <div className="text-sm text-gray-500">300/300Mbps</div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg shadow-sm">
        {/* Header with tabs and actions */}
        <div className="p-6 border-b">
          <div className="flex justify-between items-center mb-4">
            <h1 className="text-2xl font-bold">
              Package & Duration Management
            </h1>
            <div className="flex gap-2">
              <Button
                onClick={() => router.push("/app/packages/add")}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Add Package
              </Button>
              <Button
                onClick={() => router.push("/app/packages/add-duration")}
                variant="outline"
              >
                Add Duration
              </Button>
            </div>
          </div>

          {/* Tabs and Search */}
          <div className="flex justify-between items-center">
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => setActiveTab("packages")}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === "packages"
                    ? "bg-white text-blue-600 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Packages ({packagesList?.length})
              </button>
              <button
                onClick={() => setActiveTab("durations")}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === "durations"
                    ? "bg-white text-blue-600 shadow-sm"
                    : "text-gray-600 hover:text-gray-900"
                }`}
              >
                Durations ({durationsList.length})
              </button>
            </div>

            <input
              type="text"
              placeholder={`Search ${activeTab}...`}
              value={search}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setSearch(e.target.value)
              }
              className="w-[300px] p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
            />
          </div>
        </div>

        {/* Content Area */}
        <div className="p-6">
          {activeTab === "packages" ? (
            <div className="overflow-x-auto">
              <table className="min-w-full border text-sm">
                <thead className="bg-gray-100 text-left">
                  <tr>
                    <th className="border px-4 py-2">SN</th>
                    <th className="border px-4 py-2">Package Name</th>
                    <th className="border px-4 py-2">Upload Speed</th>
                    <th className="border px-4 py-2">Download Speed</th>
                    <th className="border px-4 py-2">Price</th>
                    <th className="border px-4 py-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredPackages?.map((pkg, index) => (
                    <tr key={pkg.id} className="hover:bg-gray-50">
                      <td className="border px-4 py-2">{index + 1}</td>
                      <td className="border px-4 py-2 font-medium">{pkg.package_name}</td>
                      <td className="border px-4 py-2">{pkg.upload}</td>
                      <td className="border px-4 py-2">{pkg.download}</td>
                      <td className="border px-4 py-2 text-green-600 font-medium">{pkg.price}</td>
                      <td className="border px-4 py-2">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditPackage(pkg.id)}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => handleDeletePackage(pkg.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {filteredPackages?.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No packages found matching your search.
                </div>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full border text-sm">
                <thead className="bg-gray-100 text-left">
                  <tr>
                    <th className="border px-4 py-2">SN</th>
                    <th className="border px-4 py-2">Duration</th>
                    <th className="border px-4 py-2">Description</th>
                    <th className="border px-4 py-2">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredDurations.map((duration, index) => (
                    <tr key={duration.id} className="hover:bg-gray-50">
                      <td className="border px-4 py-2">{index + 1}</td>
                      <td className="border px-4 py-2 font-medium">
                        {duration.duration}
                      </td>
                      <td className="border px-4 py-2">
                        {duration.description}
                      </td>
                      <td className="border px-4 py-2">
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditDuration(duration.id)}
                          >
                            Edit
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            onClick={() => handleDeleteDuration(duration.id)}
                          >
                            Delete
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
              {filteredDurations.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No durations found matching your search.
                </div>
              )} 
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
