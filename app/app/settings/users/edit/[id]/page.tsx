"use client";

import { useRouter, useParams } from "next/navigation";
import { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { EditUserForm } from "@/components/settings/users/edit-user";
import { User } from "@/components/settings/users/user-management";
import { ArrowLeft } from "lucide-react";

export default function EditUserPage() {
  const router = useRouter();
  const params = useParams();
  const userId = params.id as string;
  
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real application, you would fetch the user data from an API
    // For now, we'll simulate this with mock data
    const fetchUser = () => {
      // Mock user data - in reality, you'd fetch this from your API
      // This simulates the same data structure as in the users page
      const id = parseInt(userId);
      const isAdmin = id % 10 === 0;
      const mockUser: User = {
        id,
        username: isAdmin ? `admin${id}` : `user${id}`,
        email: `user${id}@example.com`,
        group: isAdmin ? "admin" : "user",
        password: isAdmin ? `Admin@${id}` : `User@${id}`,
      };

      setUser(mockUser);
      setLoading(false);
    };

    if (userId) {
      fetchUser();
    }
  }, [userId]);

  const handleSubmit = (updatedUser: User) => {
    // Here you would typically make an API call to update the user
    // For now, we'll just log the updated user and redirect back
    console.log("User updated:", updatedUser);

    // TODO: Add API call here
    // Example:
    // try {
    //   await fetch(`/api/users/${userId}`, {
    //     method: 'PUT',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(updatedUser)
    //   });
    //   // Show success message
    //   router.push("/app/settings/users");
    // } catch (error) {
    //   // Handle error
    // }
    
    // For now, just redirect back to users page
    router.push("/app/settings/users");
  };

  const handleCancel = () => {
    router.push("/app/settings/users");
  };

  if (loading) {
    return (
      <div className="p-6 max-w-2xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Loading user data...</div>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="p-6 max-w-2xl mx-auto">
        <div className="flex items-center justify-center h-64">
          <div className="text-lg text-red-600">User not found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Users
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Edit User</h1>
        <p className="text-gray-600 mt-2">
          Update user information, group, and password for <strong>{user.username}</strong>.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <EditUserForm user={user} onSubmit={handleSubmit} />
        
        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="w-full"
          >
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
