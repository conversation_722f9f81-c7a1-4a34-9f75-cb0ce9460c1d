"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { AddUserForm, User } from "@/components/settings/users/add-user";
import { ArrowLeft } from "lucide-react";

export default function AddUserPage() {
  const router = useRouter();

  const handleSubmit = (newUser: User) => {
    // Here you would typically make an API call to save the user
    // For now, we'll just log the user and redirect back to the users page
    // In a real application, you would:
    // 1. Make an API call to save the user
    // 2. Handle success/error responses
    // 3. Show success/error messages
    // 4. Redirect on success
    console.log("New user created:", newUser);

    // TODO: Add API call here
    // Example:
    // try {
    //   await fetch('/api/users', {
    //     method: 'POST',
    //     headers: { 'Content-Type': 'application/json' },
    //     body: JSON.stringify(newUser)
    //   });
    //   // Show success message
    //   router.push("/app/settings/users");
    // } catch (error) {
    //   // Handle error
    // }

    // For now, just redirect back to users page
    router.push("/app/settings/users");
  };

  const handleCancel = () => {
    router.push("/app/settings/users");
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      {/* Header with back button */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back to Users
        </Button>
      </div>

      {/* Page title */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Add New User</h1>
        <p className="text-gray-600 mt-2">
          Create a new user account with required group.
        </p>
      </div>

      {/* Form container */}
      <div className="bg-white p-6 rounded-lg border shadow-sm">
        <AddUserForm onSubmit={handleSubmit} />

        {/* Cancel button */}
        <div className="mt-6 pt-4 border-t">
          <Button variant="outline" onClick={handleCancel} className="w-full">
            Cancel
          </Button>
        </div>
      </div>
    </div>
  );
}
