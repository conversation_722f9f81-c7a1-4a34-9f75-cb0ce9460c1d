"use client";
import { useState } from "react";
import { useRouter } from "next/navigation";
import {
  OrganizationTable,
  organization,
} from "@/components/settings/organizations/organization-management";
import { Button } from "@/components/ui/button";

export default function OrganizationManagementPage() {
  const router = useRouter();

  const [organizations, setOrganizations] = useState<organization[]>([]);
  const [search, setSearch] = useState("");
  const filteredOrganizations = organizations.filter((organization) =>
    organization.name.toLowerCase().includes(search.toLowerCase())
  );

  // add max-w-4xl ml later
  return (
    <div className="p-6 space-y-8">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Organization Management Portal</h1>
        <input
          type="text"
          placeholder="Search organizations."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="mb-4 w-[250px] p-2 border rounded-lg focus:outline-none focus:ring focus:border-blue-300"
        />
        <Button
          className="ml-20"
          onClick={() => router.push("/app/settings/organizations/add")}
        >
          Add New Organization
        </Button>
      </div>

      <OrganizationTable
        organizations={filteredOrganizations}
        setOrganizations={setOrganizations}
        onDelete={(id) =>
          setOrganizations(
            organizations.filter((organization) => organization.id !== id)
          )
        }
      />
    </div>
  );
}
