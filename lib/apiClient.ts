import axios, { AxiosInstance, AxiosRequestConfig, AxiosError } from "axios"
import Cookies from "js-cookie"
import { useRefreshToken } from "@/hooks/useRefreshToken"

// Types
interface JWTPayload {
  exp: number;
  [key: string]: any;
}

interface RefreshResponse {
  success: boolean;
  error?: any;
}

interface ApiClientConfig {
  baseURL: string;
  timeout?: number;
}

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_BITFLUX_API_BASE_URL,
  timeout: 10000, // 10 seconds
});

// Singleton refresh token function
let refreshTokenInstance: (() => Promise<RefreshResponse>) | null = null;

export const setRefreshTokenInstance = (
  instance: () => Promise<RefreshResponse>
): void => {
  refreshTokenInstance = instance;
};

// Token management utilities
const TokenManager = {
  getAccessToken: (): string | undefined => {
    return Cookies.get("accessToken");
  },

  getRefreshToken: (): string | undefined => {
    return Cookies.get("refreshToken");
  },

  decodeToken: (token: string): JWTPayload | null => {
    try {
      return JSON.parse(atob(token.split(".")[1])) as JWTPayload;
    } catch (error) {
      console.error("Error decoding token:", error);
      return null;
    }
  },

  isTokenExpired: (token: string): boolean => {
    const payload = TokenManager.decodeToken(token);
    if (!payload) return true;

    const expirationTime = payload.exp * 1000;
    const currentTime = Date.now();
    const timeUntilExpiry = expirationTime - currentTime;
    
    // Token is considered expired if less than 5 minutes remaining
    return timeUntilExpiry < 5 * 60 * 1000;
  }
};

// Error handling utilities
const ErrorHandler = {
  isNetworkError: (error: any): boolean => {
    return !error.response && !error.status;
  },

  isAuthError: (error: any): boolean => {
    return error.response?.status === 401;
  },

  handleError: (error: AxiosError): Promise<never> => {
    if (ErrorHandler.isNetworkError(error)) {
      console.error("Network Error:", error.message);
    } else if (error.response) {
      console.error("API Error:", {
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers,
      });
    }
    return Promise.reject(error);
  },
};

// Request interceptor
apiClient.interceptors.request.use(
  async (config: AxiosRequestConfig): Promise<AxiosRequestConfig> => {
    // If this is a token refresh request, don't try to refresh again
    if (config.url?.includes("/token")) {
      return config;
    }

    // Add the current token to the request
    const token = TokenManager.getAccessToken();
    if (token) {
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as AxiosRequestConfig & { _retry?: boolean };
    
    // Only attempt refresh on 401 errors, not for refresh token requests
    if (
      error.response?.status === 401 && 
      !originalRequest._retry && 
      refreshTokenInstance &&
      !originalRequest.url?.includes("/token")
    ) {
      originalRequest._retry = true;

      try {
        const { success } = await refreshTokenInstance();
        if (success) {
          const newToken = TokenManager.getAccessToken();
          if (newToken) {
            originalRequest.headers = originalRequest.headers || {};
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return apiClient(originalRequest);
          }
        }
      } catch (refreshError) {
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

export default apiClient
