FROM node:20-alpine

# Set Timezone first — requires root
ENV TZ=Asia/Kathmandu

# Install tzdata before switching users
RUN apk add --no-cache tzdata && \
    cp /usr/share/zoneinfo/$TZ /etc/localtime && \
    echo $TZ > /etc/timezone

# Create working dir and set permissions
WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .

# Set correct ownership after all files copied
RUN chown -R node:node /app

# Drop privileges
USER node

EXPOSE 3000

CMD ["npm", "run", "dev"]

